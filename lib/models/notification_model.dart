import '../utils/app_logger.dart';

enum NotificationType {
  follow,
  reflex,
  comment;

  String get value {
    switch (this) {
      case NotificationType.follow:
        return 'follow';
      case NotificationType.reflex:
        return 'reflex';
      case NotificationType.comment:
        return 'comment';
    }
  }

  static NotificationType fromString(String type) {
    switch (type.toLowerCase()) {
      case 'follow':
        return NotificationType.follow;
      case 'reflex':
        return NotificationType.reflex;
      case 'comment':
        return NotificationType.comment;
      default:
        throw ArgumentError('Unknown notification type: $type');
    }
  }

  String get displayName {
    switch (this) {
      case NotificationType.follow:
        return 'New Followers';
      case NotificationType.reflex:
        return 'Reflexes';
      case NotificationType.comment:
        return 'Comments';
    }
  }

  String get description {
    switch (this) {
      case NotificationType.follow:
        return 'When someone follows you';
      case NotificationType.reflex:
        return 'When someone adds a reflex to your post';
      case NotificationType.comment:
        return 'When someone comments on your post';
    }
  }
}

class NotificationModel {
  final String id;
  final String userId;
  final NotificationType type;
  final String actorUserId;
  final String? actorUsername;
  final String? actorDisplayName;
  final String? actorAvatarUrl;
  final String title;
  final String body;
  final Map<String, dynamic>? data;
  final bool isRead;
  final DateTime createdAt;
  final DateTime updatedAt;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.actorUserId,
    this.actorUsername,
    this.actorDisplayName,
    this.actorAvatarUrl,
    required this.title,
    required this.body,
    this.data,
    required this.isRead,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    AppLogger.debug('NotificationModel.fromJson: Starting to parse notification ${json['id']}');
    
    try {
      return NotificationModel(
        id: json['id'] as String,
        userId: json['userId'] as String,
        type: NotificationType.fromString(json['type'] as String),
        actorUserId: json['actorUserId'] as String,
        actorUsername: json['actorUsername'] as String?,
        actorDisplayName: json['actorDisplayName'] as String?,
        actorAvatarUrl: json['actorAvatarUrl'] as String?,
        title: json['title'] as String,
        body: json['body'] as String,
        data: json['data'] as Map<String, dynamic>?,
        isRead: json['isRead'] as bool? ?? false,
        createdAt: DateTime.parse(json['createdAt'] as String),
        updatedAt: DateTime.parse(json['updatedAt'] as String),
      );
    } catch (e) {
      AppLogger.error('NotificationModel.fromJson: Error parsing notification', e);
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.value,
      'actorUserId': actorUserId,
      'actorUsername': actorUsername,
      'actorDisplayName': actorDisplayName,
      'actorAvatarUrl': actorAvatarUrl,
      'title': title,
      'body': body,
      'data': data,
      'isRead': isRead,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    NotificationType? type,
    String? actorUserId,
    String? actorUsername,
    String? actorDisplayName,
    String? actorAvatarUrl,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      actorUserId: actorUserId ?? this.actorUserId,
      actorUsername: actorUsername ?? this.actorUsername,
      actorDisplayName: actorDisplayName ?? this.actorDisplayName,
      actorAvatarUrl: actorAvatarUrl ?? this.actorAvatarUrl,
      title: title ?? this.title,
      body: body ?? this.body,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods for navigation
  String? get postId => data?['postId'] as String?;
  String? get reflexId => data?['reflexId'] as String?;
  String? get commentId => data?['commentId'] as String?;
  String? get followerId => data?['followerId'] as String?;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationModel(id: $id, type: $type, title: $title, isRead: $isRead)';
  }
}

class NotificationPreferences {
  final String userId;
  final bool follow;
  final bool reflex;
  final bool comment;
  final bool pushEnabled;
  final bool emailEnabled;
  final String quietHoursStart;
  final String quietHoursEnd;
  final String timezone;
  final DateTime updatedAt;

  NotificationPreferences({
    required this.userId,
    required this.follow,
    required this.reflex,
    required this.comment,
    required this.pushEnabled,
    required this.emailEnabled,
    required this.quietHoursStart,
    required this.quietHoursEnd,
    required this.timezone,
    required this.updatedAt,
  });

  factory NotificationPreferences.fromJson(Map<String, dynamic> json) {
    return NotificationPreferences(
      userId: json['userId'] as String,
      follow: json['follow'] as bool? ?? true,
      reflex: json['reflex'] as bool? ?? true,
      comment: json['comment'] as bool? ?? true,
      pushEnabled: json['pushEnabled'] as bool? ?? true,
      emailEnabled: json['emailEnabled'] as bool? ?? false,
      quietHoursStart: json['quietHoursStart'] as String? ?? '22:00',
      quietHoursEnd: json['quietHoursEnd'] as String? ?? '08:00',
      timezone: json['timezone'] as String? ?? 'UTC',
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'follow': follow,
      'reflex': reflex,
      'comment': comment,
      'pushEnabled': pushEnabled,
      'emailEnabled': emailEnabled,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
      'timezone': timezone,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  NotificationPreferences copyWith({
    String? userId,
    bool? follow,
    bool? reflex,
    bool? comment,
    bool? pushEnabled,
    bool? emailEnabled,
    String? quietHoursStart,
    String? quietHoursEnd,
    String? timezone,
    DateTime? updatedAt,
  }) {
    return NotificationPreferences(
      userId: userId ?? this.userId,
      follow: follow ?? this.follow,
      reflex: reflex ?? this.reflex,
      comment: comment ?? this.comment,
      pushEnabled: pushEnabled ?? this.pushEnabled,
      emailEnabled: emailEnabled ?? this.emailEnabled,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      timezone: timezone ?? this.timezone,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper method to get preference for a specific notification type
  bool getPreferenceForType(NotificationType type) {
    switch (type) {
      case NotificationType.follow:
        return follow;
      case NotificationType.reflex:
        return reflex;
      case NotificationType.comment:
        return comment;
    }
  }

  @override
  String toString() {
    return 'NotificationPreferences(userId: $userId, pushEnabled: $pushEnabled, follow: $follow, reflex: $reflex, comment: $comment)';
  }
}

class DeviceToken {
  final String userId;
  final String deviceId;
  final String token;
  final String platform; // 'ios' or 'android'
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  DeviceToken({
    required this.userId,
    required this.deviceId,
    required this.token,
    required this.platform,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DeviceToken.fromJson(Map<String, dynamic> json) {
    return DeviceToken(
      userId: json['userId'] as String,
      deviceId: json['deviceId'] as String,
      token: json['token'] as String,
      platform: json['platform'] as String,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'deviceId': deviceId,
      'token': token,
      'platform': platform,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'DeviceToken(deviceId: $deviceId, platform: $platform, isActive: $isActive)';
  }
}
