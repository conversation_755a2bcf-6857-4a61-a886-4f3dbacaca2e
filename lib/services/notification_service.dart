import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/notification_model.dart';
import '../services/api_service.dart';
import '../utils/app_logger.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static NotificationService get instance => _instance;

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  String? _fcmToken;
  String? _deviceId;
  bool _isInitialized = false;

  // Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('NotificationService: Initializing notification service');

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();

      // Get device info
      await _getDeviceInfo();

      // Request permissions
      await _requestPermissions();

      // Get FCM token
      await _getFCMToken();

      // Register device token with backend
      await _registerDeviceToken();

      // Set up message handlers
      _setupMessageHandlers();

      _isInitialized = true;
      AppLogger.info('NotificationService: Initialization complete');
    } catch (e) {
      AppLogger.error('NotificationService: Failed to initialize: $e');
    }
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  // Initialize Firebase messaging
  Future<void> _initializeFirebaseMessaging() async {
    // Configure Firebase messaging
    await _firebaseMessaging.setAutoInitEnabled(true);
  }

  // Get device information
  Future<void> _getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      _deviceId = androidInfo.id;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      _deviceId = iosInfo.identifierForVendor;
    }

    AppLogger.debug('NotificationService: Device ID: $_deviceId');
  }

  // Request notification permissions
  Future<bool> _requestPermissions() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      final isGranted =
          settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;

      AppLogger.info('NotificationService: Permission granted: $isGranted');
      return isGranted;
    } catch (e) {
      AppLogger.error('NotificationService: Failed to request permissions: $e');
      return false;
    }
  }

  // Get FCM token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      AppLogger.debug('NotificationService: FCM Token: $_fcmToken');

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        AppLogger.debug('NotificationService: FCM Token refreshed: $newToken');
        _registerDeviceToken(); // Re-register with new token
      });
    } catch (e) {
      AppLogger.error('NotificationService: Failed to get FCM token: $e');
    }
  }

  // Register device token with backend
  Future<void> _registerDeviceToken() async {
    if (_fcmToken == null || _deviceId == null) {
      AppLogger.warning(
        'NotificationService: Cannot register device token - missing token or device ID',
      );
      return;
    }

    try {
      final platform = Platform.isIOS ? 'ios' : 'android';

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/device-tokens',
        body: {'deviceId': _deviceId, 'token': _fcmToken, 'platform': platform},
      );

      if (response.statusCode == 200) {
        AppLogger.info(
          'NotificationService: Device token registered successfully',
        );
      } else {
        AppLogger.error(
          'NotificationService: Failed to register device token: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error registering device token: $e',
      );
    }
  }

  // Unregister device token
  Future<void> unregisterDeviceToken() async {
    if (_deviceId == null) return;

    try {
      final response = await ApiService.instance.makeRequest(
        method: 'DELETE',
        path: '/device-tokens/$_deviceId',
      );

      if (response.statusCode == 200) {
        AppLogger.info(
          'NotificationService: Device token unregistered successfully',
        );
      } else {
        AppLogger.error(
          'NotificationService: Failed to unregister device token: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error unregistering device token: $e',
      );
    }
  }

  // Set up message handlers
  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    _firebaseMessaging.getInitialMessage().then((message) {
      if (message != null) {
        _handleNotificationTap(message);
      }
    });
  }

  // Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    AppLogger.info(
      'NotificationService: Received foreground message: ${message.messageId}',
    );

    // Show local notification when app is in foreground
    await _showLocalNotification(message);
  }

  // Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    AppLogger.info(
      'NotificationService: Received background message: ${message.messageId}',
    );
    // Background messages are automatically displayed by the system
  }

  // Handle notification tap
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    AppLogger.info(
      'NotificationService: Notification tapped: ${message.messageId}',
    );

    // Navigate based on notification data
    final data = message.data;
    final type = data['type'] as String?;

    if (type != null) {
      await _navigateFromNotification(type, data);
    }
  }

  // Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    const androidDetails = AndroidNotificationDetails(
      'gameflex_notifications',
      'GameFlex Notifications',
      channelDescription: 'Notifications from GameFlex',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'GameFlex',
      message.notification?.body ?? '',
      details,
      payload: jsonEncode(message.data),
    );
  }

  // Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!) as Map<String, dynamic>;
        final type = data['type'] as String?;

        if (type != null) {
          _navigateFromNotification(type, data);
        }
      } catch (e) {
        AppLogger.error(
          'NotificationService: Error parsing notification payload: $e',
        );
      }
    }
  }

  // Navigate based on notification type
  Future<void> _navigateFromNotification(
    String type,
    Map<String, dynamic> data,
  ) async {
    // This would typically use a navigation service or router
    // For now, we'll just log the navigation intent
    AppLogger.info('NotificationService: Navigate to $type with data: $data');

    // TODO: Implement actual navigation logic based on your app's routing
    // Example:
    // switch (type) {
    //   case 'follow':
    //     NavigationService.navigateToProfile(data['followerId']);
    //     break;
    //   case 'reflex':
    //     NavigationService.navigateToPost(data['postId']);
    //     break;
    //   case 'comment':
    //     NavigationService.navigateToPost(data['postId']);
    //     break;
    // }
  }

  // API methods for notification management
  Future<List<NotificationModel>> getNotifications({
    int limit = 20,
    String? lastKey,
  }) async {
    try {
      final queryParams = <String, String>{'limit': limit.toString()};

      if (lastKey != null) {
        queryParams['lastKey'] = lastKey;
      }

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/notifications',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final notifications =
            (data['notifications'] as List)
                .map((json) => NotificationModel.fromJson(json))
                .toList();

        return notifications;
      } else {
        throw Exception('Failed to get notifications: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('NotificationService: Error getting notifications: $e');
      rethrow;
    }
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'PUT',
        path: '/notifications/$notificationId',
        body: {},
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to mark notification as read: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error marking notification as read: $e',
      );
      rethrow;
    }
  }

  Future<NotificationPreferences> getNotificationPreferences() async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/notification-preferences',
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return NotificationPreferences.fromJson(data);
      } else {
        throw Exception(
          'Failed to get notification preferences: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error getting notification preferences: $e',
      );
      rethrow;
    }
  }

  Future<void> updateNotificationPreferences(
    NotificationPreferences preferences,
  ) async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'PUT',
        path: '/notification-preferences',
        body: preferences.toJson(),
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to update notification preferences: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error updating notification preferences: $e',
      );
      rethrow;
    }
  }

  // Getters
  String? get fcmToken => _fcmToken;
  String? get deviceId => _deviceId;
  bool get isInitialized => _isInitialized;
}
